<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Envoy Gateway Admin</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        .nav {
            background-color: #34495e;
            padding: 0;
            margin: 0;
        }
        .nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
        }
        .nav li {
            margin: 0;
        }
        .nav a {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
        }
        .nav a:hover, .nav a.active {
            background-color: #3498db;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            padding: 1.5rem;
            display: none;
        }
        .section.active {
            display: block;
        }
        .section h2 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }
        .error {
            background-color: #e74c3c;
            color: white;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
        }
        .stat-card h4 {
            margin: 0 0 0.5rem 0;
            color: #495057;
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #28a745;
        }
        .resources-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .resources-table th,
        .resources-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .resources-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status-true {
            background-color: #d4edda;
            color: #155724;
        }
        .status-false {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-unknown {
            background-color: #fff3cd;
            color: #856404;
        }
        .pprof-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .pprof-link {
            display: block;
            padding: 1rem;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            text-decoration: none;
            color: #495057;
            transition: all 0.3s;
        }
        .pprof-link:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 1rem;
        }
        .refresh-btn:hover {
            background: #2980b9;
        }
        pre {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Envoy Gateway Admin</h1>
    </div>
    
    <nav class="nav">
        <ul>
            <li><a href="#" onclick="showSection('overview')" class="nav-link active">Overview</a></li>
            <li><a href="#" onclick="showSection('resources')" class="nav-link">Resources</a></li>
            <li><a href="#" onclick="showSection('stats')" class="nav-link">Stats</a></li>
            <li><a href="#" onclick="showSection('pprof')" class="nav-link">Profiling</a></li>
        </ul>
    </nav>

    <div class="container">
        <!-- Overview Section -->
        <div id="overview" class="section active">
            <h2>Overview</h2>
            <div class="loading" id="overview-loading">Loading...</div>
            <div id="overview-content"></div>
        </div>

        <!-- Resources Section -->
        <div id="resources" class="section">
            <h2>Gateway Resources</h2>
            <button class="refresh-btn" onclick="loadResources()">Refresh</button>
            <div class="loading" id="resources-loading">Loading...</div>
            <div id="resources-content"></div>
        </div>

        <!-- Stats Section -->
        <div id="stats" class="section">
            <h2>Statistics</h2>
            <button class="refresh-btn" onclick="loadStats()">Refresh</button>
            <div class="loading" id="stats-loading">Loading...</div>
            <div id="stats-content"></div>
        </div>

        <!-- Profiling Section -->
        <div id="pprof" class="section">
            <h2>Performance Profiling</h2>
            <p>Access profiling endpoints for performance analysis:</p>
            <div class="pprof-links">
                <a href="/debug/pprof/" class="pprof-link" target="_blank">
                    <strong>Profile Index</strong><br>
                    <small>Overview of all available profiles</small>
                </a>
                <a href="/debug/pprof/goroutine" class="pprof-link" target="_blank">
                    <strong>Goroutines</strong><br>
                    <small>Stack traces of all current goroutines</small>
                </a>
                <a href="/debug/pprof/heap" class="pprof-link" target="_blank">
                    <strong>Heap</strong><br>
                    <small>Memory allocation sampling</small>
                </a>
                <a href="/debug/pprof/profile" class="pprof-link" target="_blank">
                    <strong>CPU Profile</strong><br>
                    <small>30-second CPU profile</small>
                </a>
                <a href="/debug/pprof/trace?seconds=5" class="pprof-link" target="_blank">
                    <strong>Trace</strong><br>
                    <small>5-second execution trace</small>
                </a>
                <a href="/debug/pprof/cmdline" class="pprof-link" target="_blank">
                    <strong>Command Line</strong><br>
                    <small>Command line invocation</small>
                </a>
            </div>
        </div>
    </div>

    <script>
        // Navigation
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Add active class to clicked nav link
            event.target.classList.add('active');
            
            // Load data for the section
            switch(sectionId) {
                case 'overview':
                    loadOverview();
                    break;
                case 'resources':
                    loadResources();
                    break;
                case 'stats':
                    loadStats();
                    break;
            }
        }

        // API calls
        async function fetchData(endpoint) {
            try {
                const response = await fetch(endpoint);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error('Fetch error:', error);
                throw error;
            }
        }

        function showError(containerId, message) {
            document.getElementById(containerId).innerHTML = 
                `<div class="error">Error: ${message}</div>`;
        }

        function hideLoading(loadingId) {
            const loading = document.getElementById(loadingId);
            if (loading) loading.style.display = 'none';
        }

        // Load Overview
        async function loadOverview() {
            try {
                const data = await fetchData('/admin/api/status');
                hideLoading('overview-loading');
                
                document.getElementById('overview-content').innerHTML = `
                    <div class="stats-grid">
                        <div class="stat-card">
                            <h4>Version</h4>
                            <div class="stat-value">${data.version || 'Unknown'}</div>
                        </div>
                        <div class="stat-card">
                            <h4>Uptime</h4>
                            <div class="stat-value">${data.uptime || 'Unknown'}</div>
                        </div>
                        <div class="stat-card">
                            <h4>Status</h4>
                            <div class="stat-value">${data.status || 'Running'}</div>
                        </div>
                        <div class="stat-card">
                            <h4>Configuration</h4>
                            <div class="stat-value">${data.configStatus || 'Loaded'}</div>
                        </div>
                    </div>
                    <h3>Configuration Details</h3>
                    <pre>${JSON.stringify(data.config || {}, null, 2)}</pre>
                `;
            } catch (error) {
                hideLoading('overview-loading');
                showError('overview-content', error.message);
            }
        }

        // Load Resources
        async function loadResources() {
            try {
                const data = await fetchData('/admin/api/resources');
                hideLoading('resources-loading');
                
                let html = '';
                for (const [resourceType, resources] of Object.entries(data)) {
                    html += `
                        <h3>${resourceType}</h3>
                        <table class="resources-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Namespace</th>
                                    <th>Status</th>
                                    <th>Reason</th>
                                    <th>Message</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    resources.forEach(resource => {
                        const statusClass = resource.status === 'True' ? 'status-true' : 
                                          resource.status === 'False' ? 'status-false' : 'status-unknown';
                        html += `
                            <tr>
                                <td>${resource.name}</td>
                                <td>${resource.namespace || '-'}</td>
                                <td><span class="status-badge ${statusClass}">${resource.status}</span></td>
                                <td>${resource.reason || '-'}</td>
                                <td>${resource.message || '-'}</td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table>';
                }
                
                document.getElementById('resources-content').innerHTML = html;
            } catch (error) {
                hideLoading('resources-loading');
                showError('resources-content', error.message);
            }
        }

        // Load Stats
        async function loadStats() {
            try {
                const data = await fetchData('/admin/api/stats');
                hideLoading('stats-loading');
                
                let html = '<div class="stats-grid">';
                for (const [category, stats] of Object.entries(data)) {
                    html += `
                        <div class="stat-card">
                            <h4>${category}</h4>
                            <pre>${JSON.stringify(stats, null, 2)}</pre>
                        </div>
                    `;
                }
                html += '</div>';
                
                document.getElementById('stats-content').innerHTML = html;
            } catch (error) {
                hideLoading('stats-loading');
                showError('stats-content', error.message);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadOverview();
        });
    </script>
</body>
</html>
